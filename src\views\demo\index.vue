<script setup lang="ts">
/** 待办事项数据类型定义 */
interface ITodoItem {
  /** 待办事项唯一标识 */
  id: string
  /** 待办事项内容 */
  content: string
  /** 是否已完成 */
  completed: boolean
  /** 创建时间 */
  createdAt: Date
}

// * 响应式数据定义
/** 新添加的待办事项输入内容 */
let newTodo = $ref<string>('')

/** 待办事项列表 */
let todoList = $ref<ITodoItem[]>([])

// * 计算属性
/** 已完成任务数量 */
const completedCount = $computed(() => todoList.filter(todo => todo.completed).length)

/** 待完成任务数量 */
const pendingCount = $computed(() => todoList.filter(todo => !todo.completed).length)

/** 是否全部完成 */
const allCompleted = $computed(() => todoList.length > 0 && completedCount === todoList.length)

// * 核心功能函数

/** 添加新的待办事项 */
function addTodo(): void {
  const content = newTodo.trim()
  if (!content) return

  const newItem: ITodoItem = {
    id: generateId(),
    content,
    completed: false,
    createdAt: new Date(),
  }

  todoList.unshift(newItem)
  newTodo = '' // 清空输入框
}

/** 切换待办事项完成状态 */
function toggleTodo(id: string): void {
  const todo = todoList.find(item => item.id === id)
  if (todo)
    todo.completed = !todo.completed
}

/** 删除待办事项 */
function deleteTodo(id: string): void {
  const index = todoList.findIndex(item => item.id === id)
  if (index > -1)
    todoList.splice(index, 1)
}

/** 切换全部完成状态 */
function toggleAllComplete(): void {
  const targetState = !allCompleted
  todoList.forEach((todo) => {
    todo.completed = targetState
  })
}

/** 清空所有已完成的待办事项 */
function clearCompleted(): void {
  todoList = todoList.filter(todo => !todo.completed)
}

// * 工具函数

/** 生成唯一ID */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

/** 格式化时间显示 */
function formatTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return date.toLocaleDateString('zh-CN')
}
</script>

<template>
  <div class="min-h-screen bg-[#f8fafc] p-[20px]">
    <div class="max-w-[600px] mx-auto">
      <!-- 标题区域 -->
      <div class="text-center mb-[40px]">
        <h1 class="text-[32px] font-bold text-[#1e293b] mb-[8px]">
          📝 Todo List
        </h1>
        <p class="text-[16px] text-[#64748b]">
          高效管理您的日常任务
        </p>
      </div>

      <!-- 添加任务区域 -->
      <div class="bg-white rounded-[12px] shadow-sm p-[24px] mb-[24px]">
        <div class="flex gap-[12px]">
          <input
            v-model="newTodo"
            type="text"
            placeholder="输入新的待办事项..."
            class="input-field"
            @keyup.enter="addTodo"
          />
          <button
            :disabled="!newTodo.trim()"
            class="add-btn"
            @click="addTodo"
          >
            ➕ 添加
          </button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="bg-white rounded-[12px] shadow-sm p-[20px] mb-[24px]">
        <div class="flex justify-between items-center text-[14px]">
          <span class="text-[#64748b]">
            总计：<span class="font-semibold text-[#1e293b]">{{ todoList.length }}</span> 项
          </span>
          <span class="text-[#64748b]">
            已完成：<span class="font-semibold text-[#10b981]">{{ completedCount }}</span> 项
          </span>
          <span class="text-[#64748b]">
            待完成：<span class="font-semibold text-[#f59e0b]">{{ pendingCount }}</span> 项
          </span>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div v-if="todoList.length > 0" class="flex justify-between mb-[24px]">
        <button
          class="action-btn bg-[#3b82f6] hover:bg-[#2563eb]"
          @click="toggleAllComplete"
        >
          {{ allCompleted ? '❌ 取消全选' : '✅ 全选' }}
        </button>

        <button
          :disabled="completedCount === 0"
          class="action-btn bg-[#ef4444] hover:bg-[#dc2626] disabled:bg-[#e5e7eb] disabled:text-[#9ca3af]"
          @click="clearCompleted"
        >
          🗑️ 清空已完成
        </button>
      </div>

      <!-- 任务列表 -->
      <div class="space-y-[12px]">
        <TransitionGroup name="todo" tag="div">
          <div
            v-for="todo in todoList"
            :key="todo.id"
            class="todo-item"
            :class="{ completed: todo.completed }"
          >
            <div class="flex items-center gap-[12px]">
              <!-- 完成状态复选框 -->
              <button
                class="todo-checkbox"
                :class="{ checked: todo.completed }"
                @click="toggleTodo(todo.id)"
              >
                <span v-if="todo.completed" class="text-[14px]">✓</span>
              </button>

              <!-- 任务内容 -->
              <span
                class="todo-content"
                :class="{ 'line-through text-[#9ca3af]': todo.completed }"
              >
                {{ todo.content }}
              </span>

              <!-- 创建时间 -->
              <span class="todo-time">
                {{ formatTime(todo.createdAt) }}
              </span>
            </div>

            <!-- 删除按钮 -->
            <button
              class="delete-btn"
              @click="deleteTodo(todo.id)"
            >
              🗑️
            </button>
          </div>
        </TransitionGroup>
      </div>

      <!-- 空状态 -->
      <div v-if="todoList.length === 0" class="empty-state">
        <div class="text-[48px] mb-[16px]">
          📝
        </div>
        <h3 class="text-[18px] font-semibold text-[#64748b] mb-[8px]">
          暂无待办事项
        </h3>
        <p class="text-[14px] text-[#9ca3af]">
          添加您的第一个任务开始吧！
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 输入框样式 */
.input-field {
  @apply flex-1 px-[16px] py-[12px] border-[1px] border-[#e2e8f0] rounded-[8px] text-[16px]
         focus:outline-none focus:border-[#3b82f6] focus:ring-[2px] focus:ring-[#3b82f6]/20
         transition-all duration-200;
}

/* 添加按钮样式 */
.add-btn {
  @apply px-[20px] py-[12px] bg-[#3b82f6] text-white text-[16px] font-medium rounded-[8px]
         hover:bg-[#2563eb] disabled:bg-[#e5e7eb] disabled:text-[#9ca3af] disabled:cursor-not-allowed
         transition-all duration-200;
}

/* 操作按钮样式 */
.action-btn {
  @apply px-[16px] py-[8px] text-white text-[14px] font-medium rounded-[8px]
         transition-all duration-200 cursor-pointer;
}

/* 待办事项容器样式 */
.todo-item {
  @apply bg-white rounded-[12px] shadow-sm p-[16px] flex items-center justify-between
         hover:shadow-md transition-all duration-200;
}

.todo-item.completed {
  @apply bg-[#f8fafc];
}

/* 复选框样式 */
.todo-checkbox {
  @apply w-[20px] h-[20px] rounded-[4px] border-[2px] border-[#e2e8f0]
         flex items-center justify-center transition-all duration-200
         hover:border-[#3b82f6];
}

.todo-checkbox.checked {
  @apply bg-[#10b981] border-[#10b981] text-white;
}

/* 任务内容样式 */
.todo-content {
  @apply flex-1 text-[16px] text-[#1e293b] font-medium;
}

/* 时间显示样式 */
.todo-time {
  @apply text-[12px] text-[#9ca3af] ml-auto mr-[12px];
}

/* 删除按钮样式 */
.delete-btn {
  @apply w-[32px] h-[32px] rounded-[6px] hover:bg-[#fee2e2] transition-all duration-200
         flex items-center justify-center text-[14px];
}

/* 空状态样式 */
.empty-state {
  @apply text-center py-[60px] bg-white rounded-[12px] shadow-sm;
}

/* 过渡动画 */
.todo-enter-active,
.todo-leave-active {
  @apply transition-all duration-300;
}

.todo-enter-from {
  @apply opacity-0 transform translate-y-[-10px];
}

.todo-leave-to {
  @apply opacity-0 transform translate-x-[20px];
}
</style>
